from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_mail import Mail, Message
from werkzeug.utils import secure_filename
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import os
import uuid
from models import db, Item
from forms import ItemForm, SearchForm, ConfigForm, LoginForm
from PIL import Image, ImageOps
import json
from urllib.parse import quote

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-this-in-production'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///titas_baby_shop.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Mail configuration (for sending reservation emails)
app.config['MAIL_SERVER'] = 'smtp.gmail.com'  # Default to Gmail, can be changed
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USE_SSL'] = False
app.config['MAIL_USERNAME'] = '<EMAIL>'  # Set via environment variable
app.config['MAIL_PASSWORD'] = 'zplo lwpd bohn aqrc'  # Set via environment variable
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'  # Set via environment variable

# Admin configuration
ADMIN_USERNAME = 'nikita'
ADMIN_PASSWORD_HASH = generate_password_hash('Fa210808!')  # Change this password!

# Upload configuration
UPLOAD_FOLDER = 'static/uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Initialize database and mail
db.init_app(app)
mail = Mail(app)

def login_required(f):
    """Decorator to require admin login for protected routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            flash('Je moet inloggen om deze pagina te bekijken.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}

def resize_image(image_path, max_size=(800, 800)):
    """Resize image to reduce file size while maintaining aspect ratio and fix orientation"""
    try:
        with Image.open(image_path) as img:
            # Fix orientation based on EXIF data
            img = ImageOps.exif_transpose(img)

            # Resize image
            img.thumbnail(max_size, Image.Resampling.LANCZOS)

            # Convert to RGB if necessary (for JPEG saving)
            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')

            # Save with optimization
            img.save(image_path, 'JPEG', optimize=True, quality=85)
    except Exception as e:
        print(f"Error processing image: {e}")

def rotate_image(image_path, degrees):
    """Rotate image by specified degrees (90, 180, 270)"""
    try:
        with Image.open(image_path) as img:
            # Rotate image - Note: PIL's ROTATE_90 is counterclockwise, so we swap for intuitive behavior
            if degrees == 90:
                img = img.transpose(Image.Transpose.ROTATE_270)  # 90° clockwise = 270° counterclockwise
            elif degrees == 180:
                img = img.transpose(Image.Transpose.ROTATE_180)
            elif degrees == 270:
                img = img.transpose(Image.Transpose.ROTATE_90)   # 270° clockwise = 90° counterclockwise
            else:
                return False

            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'P'):
                img = img.convert('RGB')

            # Save the rotated image
            img.save(image_path, 'JPEG', optimize=True, quality=85)
            return True
    except Exception as e:
        print(f"Error rotating image: {e}")
        return False

@app.route('/')
def index():
    # Public gallery page - no login required
    # Get filter parameters
    geslacht_filter = request.args.get('geslacht', '')
    maat_filter = request.args.get('maat', '')
    brand_filter = request.args.get('brand', '')
    category_filter = request.args.get('category', '')
    search_query = request.args.get('search', '')
    sort_by = request.args.get('sort', 'newest')  # newest, oldest, price_low, price_high

    # Build query
    query = Item.query

    # Apply filters
    if geslacht_filter:
        query = query.filter(Item.geslacht == geslacht_filter)
    if maat_filter:
        query = query.filter(Item.maat == maat_filter)
    if brand_filter:
        query = query.filter(Item.brand == brand_filter)
    if category_filter:
        query = query.filter(Item.category == category_filter)
    if search_query:
        query = query.filter(
            db.or_(
                Item.brand.contains(search_query),
                Item.category.contains(search_query),
                Item.geslacht.contains(search_query),
                Item.maat.contains(search_query)
            )
        )

    # Apply sorting
    if sort_by == 'oldest':
        query = query.order_by(Item.created_at.asc())
    elif sort_by == 'price_low':
        query = query.order_by(Item.prijs.asc())
    elif sort_by == 'price_high':
        query = query.order_by(Item.prijs.desc())
    else:  # newest (default)
        query = query.order_by(Item.created_at.desc())

    items = query.all()

    # Load config for filter options
    config = load_config()

    return render_template('gallery.html',
                         items=items,
                         config=config,
                         filters={
                             'geslacht': geslacht_filter,
                             'maat': maat_filter,
                             'brand': brand_filter,
                             'category': category_filter,
                             'search': search_query,
                             'sort': sort_by
                         })

@app.route('/admin')
def admin():
    if session.get('logged_in'):
        return redirect(url_for('manage'))
    else:
        return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    # If already logged in, redirect to manage page
    if session.get('logged_in'):
        return redirect(url_for('manage'))

    form = LoginForm()

    if form.validate_on_submit():
        username = form.username.data
        password = form.password.data

        if username == ADMIN_USERNAME and check_password_hash(ADMIN_PASSWORD_HASH, password):
            session['logged_in'] = True
            session['username'] = username
            flash('Succesvol ingelogd!', 'success')

            # Redirect to the page they were trying to access, or manage page
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('manage'))
        else:
            flash('Ongeldige gebruikersnaam of wachtwoord.', 'error')

    return render_template('login.html', form=form)

@app.route('/logout')
def logout():
    session.clear()
    flash('Je bent uitgelogd.', 'success')
    return redirect(url_for('index'))

@app.route('/basket')
def basket():
    """Display the shopping basket page"""
    return render_template('basket.html')

@app.route('/api/basket/add/<item_id>', methods=['POST'])
def add_to_basket(item_id):
    """Add an item to the basket (AJAX endpoint)"""
    item = Item.query.get_or_404(item_id)
    return jsonify({
        'success': True,
        'message': f'Item {item.brand} - {item.category} toegevoegd aan winkelmandje',
        'item': item.to_dict()
    })

@app.route('/api/basket/remove/<item_id>', methods=['POST'])
def remove_from_basket(item_id):
    """Remove an item from the basket (AJAX endpoint)"""
    item = Item.query.get_or_404(item_id)
    return jsonify({
        'success': True,
        'message': f'Item {item.brand} - {item.category} verwijderd uit winkelmandje'
    })

@app.route('/api/basket/clear', methods=['POST'])
def clear_basket():
    """Clear the entire basket (AJAX endpoint)"""
    return jsonify({
        'success': True,
        'message': 'Winkelmandje geleegd'
    })

@app.route('/api/basket/items', methods=['POST'])
def get_basket_items():
    """Get full item details for items in basket"""
    data = request.get_json()
    item_ids = data.get('item_ids', [])

    if not item_ids:
        return jsonify({'items': [], 'total': 0})

    items = Item.query.filter(Item.id.in_(item_ids)).all()
    items_data = [item.to_dict() for item in items]
    total = sum(item.prijs for item in items)

    return jsonify({
        'items': items_data,
        'total': total
    })

@app.route('/api/basket/send-email', methods=['POST'])
def send_reservation_email():
    """Send reservation email with basket contents"""
    try:
        data = request.get_json()
        item_ids = data.get('item_ids', [])
        customer_info = data.get('customer_info', {})

        if not item_ids:
            return jsonify({'success': False, 'message': 'Geen items in winkelmandje'}), 400

        # Get item details
        items = Item.query.filter(Item.id.in_(item_ids)).all()
        if not items:
            return jsonify({'success': False, 'message': 'Items niet gevonden'}), 404

        # Load email configuration
        config = load_config()
        email_address = config.get('email_address')
        email_subject = config.get('email_subject', 'Nieuwe Reservering - Tita\'s Baby Shop')

        if not email_address:
            return jsonify({'success': False, 'message': 'Email adres niet geconfigureerd'}), 500

        # Check if mail is configured
        if not app.config.get('MAIL_USERNAME'):
            return jsonify({'success': False, 'message': 'Email server niet geconfigureerd. Gebruik de "Email App Openen" optie.'}), 500

        # Create email content
        total = sum(item.prijs for item in items)

        email_body = f"""
Nieuwe reservering ontvangen via Tita's Baby Shop website.

Klant informatie:
Naam: {customer_info.get('name', 'Niet opgegeven')}
Email: {customer_info.get('email', 'Niet opgegeven')}
Telefoon: {customer_info.get('phone', 'Niet opgegeven')}

Geselecteerde items:
"""

        for item in items:
            email_body += f"""
- Item ID: {item.id}
  Geslacht: {item.geslacht}
  Maat: {item.maat}
  Brand: {item.brand}
  Category: {item.category}
  Prijs: €{item.prijs:.2f}
"""

        email_body += f"""
Totaal: €{total:.2f}

Deze reservering is automatisch gegenereerd via de website.
"""

        # Send email
        msg = Message(
            subject=email_subject,
            recipients=[email_address],
            body=email_body
        )

        mail.send(msg)

        return jsonify({
            'success': True,
            'message': 'Reservering email succesvol verzonden!'
        })

    except Exception as e:
        print(f"Error sending email: {e}")
        return jsonify({
            'success': False,
            'message': f'Fout bij verzenden email: {str(e)}'
        }), 500

@app.route('/api/basket/mailto-link', methods=['POST'])
def generate_mailto_link():
    """Generate mailto link for opening user's email client"""
    try:
        data = request.get_json()
        item_ids = data.get('item_ids', [])
        customer_info = data.get('customer_info', {})

        if not item_ids:
            return jsonify({'success': False, 'message': 'Geen items in winkelmandje'}), 400

        # Get item details
        items = Item.query.filter(Item.id.in_(item_ids)).all()
        if not items:
            return jsonify({'success': False, 'message': 'Items niet gevonden'}), 404

        # Load email configuration
        config = load_config()
        email_address = config.get('email_address', '<EMAIL>')
        email_subject = config.get('email_subject', 'Nieuwe Reservering - Tita\'s Baby Shop')

        # Create email content
        total = sum(item.prijs for item in items)

        email_body = f"""Beste Tita's Baby Shop,

Ik wil graag de volgende items reserveren:

Mijn gegevens:
Naam: {customer_info.get('name', '[Vul je naam in]')}
Email: {customer_info.get('email', '[Vul je email in]')}
Telefoon: {customer_info.get('phone', '[Vul je telefoonnummer in]')}

Geselecteerde items:
"""

        for item in items:
            email_body += f"""
- Item ID: {item.id}
  Geslacht: {item.geslacht}
  Maat: {item.maat}
  Brand: {item.brand}
  Category: {item.category}
  Prijs: €{item.prijs:.2f}
"""

        email_body += f"""
Totaal: €{total:.2f}

Graag hoor ik van jullie wanneer ik de items kan ophalen.

Met vriendelijke groet,
[Je naam]
"""

        # Create mailto link
        mailto_link = f"mailto:{email_address}?subject={quote(email_subject)}&body={quote(email_body)}"

        return jsonify({
            'success': True,
            'mailto_link': mailto_link,
            'message': 'Email link gegenereerd!'
        })

    except Exception as e:
        print(f"Error generating mailto link: {e}")
        return jsonify({
            'success': False,
            'message': f'Fout bij genereren email link: {str(e)}'
        }), 500

@app.route('/new', methods=['GET', 'POST'])
@login_required
def new_item():
    form = ItemForm()
    
    if form.validate_on_submit():
        # Handle file upload
        foto_filename = None
        if form.foto.data:
            file = form.foto.data
            if file and allowed_file(file.filename):
                # Generate unique filename
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                
                file.save(file_path)
                resize_image(file_path)  # Resize to reduce file size
                foto_filename = unique_filename
        
        # Create new item
        item = Item(
            geslacht=form.geslacht.data,
            maat=form.maat.data,
            prijs=form.prijs.data,
            brand=form.brand.data,
            category=form.category.data,
            foto_filename=foto_filename
        )
        
        db.session.add(item)
        db.session.commit()
        
        flash(f'Item {item.id} succesvol toegevoegd!', 'success')
        return redirect(url_for('manage'))
    
    return render_template('new.html', form=form)

@app.route('/manage')
@login_required
def manage():
    search_form = SearchForm()
    search_query = request.args.get('search', '')

    if search_query:
        items = Item.query.filter(
            db.or_(
                Item.id.contains(search_query),
                Item.brand.contains(search_query),
                Item.category.contains(search_query),
                Item.geslacht.contains(search_query),
                Item.maat.contains(search_query)
            )
        ).order_by(Item.created_at.desc()).all()
    else:
        items = Item.query.order_by(Item.created_at.desc()).all()

    return render_template('manage.html', items=items, search_form=search_form, search_query=search_query)

@app.route('/delete/<item_id>', methods=['GET', 'POST'])
@login_required
def delete_item(item_id):
    try:
        # Force find the item - if it doesn't exist, we'll get a 404
        item = Item.query.get_or_404(item_id)

        # Store item info for success message before deletion
        item_display = f"{item.brand} {item.category} (ID: {item_id[:8]}...)"

        # Delete associated image file if exists
        if item.foto_filename:
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], item.foto_filename)
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except OSError as e:
                    # Log the error but don't fail the deletion
                    print(f"Warning: Could not delete image file {file_path}: {e}")

        # Force delete the item from database
        db.session.delete(item)
        db.session.commit()

        flash(f'Item {item_display} succesvol verwijderd!', 'success')
        return redirect(url_for('manage'))

    except Exception as e:
        # Force rollback and try again with more aggressive approach
        db.session.rollback()

        try:
            # Try to delete directly with SQL if ORM fails
            db.session.execute(db.text("DELETE FROM item WHERE id = :item_id"), {"item_id": item_id})
            db.session.commit()
            flash(f'Item {item_id[:8]}... geforceerd verwijderd!', 'warning')
        except Exception as e2:
            db.session.rollback()
            flash(f'Fout bij verwijderen: {str(e)}. Tweede poging mislukt: {str(e2)}', 'error')

        return redirect(url_for('manage'))

def load_config():
    """Load configuration from config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_config(config_data):
    """Save configuration to config.json"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)

@app.route('/config', methods=['GET', 'POST'])
@login_required
def config_page():
    form = ConfigForm()

    if form.validate_on_submit():
        try:
            # Get config data from form
            config_data = form.get_config_dict()

            # Validate that list fields have at least one option
            list_fields = ['geslacht', 'maat', 'brand', 'category']
            for field_name in list_fields:
                values = config_data.get(field_name, [])
                if not values:
                    flash(f'Het veld "{field_name}" moet minimaal één waarde bevatten.', 'error')
                    return render_template('config.html', form=form)

            # Validate that email fields are not empty
            if not config_data.get('email_address'):
                flash('Email adres is verplicht.', 'error')
                return render_template('config.html', form=form)

            if not config_data.get('email_subject'):
                flash('Email onderwerp is verplicht.', 'error')
                return render_template('config.html', form=form)

            # Save the configuration
            save_config(config_data)

            flash('Configuratie succesvol opgeslagen! De nieuwe opties zijn nu beschikbaar in het formulier.', 'success')
            return redirect(url_for('config_page'))

        except Exception as e:
            flash(f'Fout bij het opslaan van de configuratie: {str(e)}', 'error')

    return render_template('config.html', form=form)

@app.route('/rotate_image/<item_id>/<int:degrees>', methods=['POST'])
@login_required
def rotate_image_endpoint(item_id, degrees):
    """Rotate an item's image by specified degrees"""
    if degrees not in [90, 180, 270]:
        return jsonify({'success': False, 'message': 'Invalid rotation degrees'}), 400

    try:
        item = Item.query.get_or_404(item_id)

        if not item.foto_filename:
            return jsonify({'success': False, 'message': 'No image found for this item'}), 404

        image_path = os.path.join(app.config['UPLOAD_FOLDER'], item.foto_filename)

        if not os.path.exists(image_path):
            return jsonify({'success': False, 'message': 'Image file not found'}), 404

        if rotate_image(image_path, degrees):
            return jsonify({'success': True, 'message': f'Image rotated {degrees} degrees'})
        else:
            return jsonify({'success': False, 'message': 'Failed to rotate image'}), 500

    except Exception as e:
        print(f"Error in rotate_image_endpoint: {e}")
        return jsonify({'success': False, 'message': 'Server error'}), 500

@app.route('/rotate_image_public/<item_id>/<int:degrees>', methods=['POST'])
def rotate_image_public_endpoint(item_id, degrees):
    """Public endpoint to rotate an item's image (for gallery view)"""
    if degrees not in [90, 180, 270]:
        return jsonify({'success': False, 'message': 'Invalid rotation degrees'}), 400

    try:
        item = Item.query.get_or_404(item_id)

        if not item.foto_filename:
            return jsonify({'success': False, 'message': 'No image found for this item'}), 404

        image_path = os.path.join(app.config['UPLOAD_FOLDER'], item.foto_filename)

        if not os.path.exists(image_path):
            return jsonify({'success': False, 'message': 'Image file not found'}), 404

        if rotate_image(image_path, degrees):
            return jsonify({'success': True, 'message': f'Image rotated {degrees} degrees'})
        else:
            return jsonify({'success': False, 'message': 'Failed to rotate image'}), 500

    except Exception as e:
        print(f"Error in rotate_image_public_endpoint: {e}")
        return jsonify({'success': False, 'message': 'Server error'}), 500

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
