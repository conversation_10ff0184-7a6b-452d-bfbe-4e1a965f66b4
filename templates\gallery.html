<!DOCTYPE html>
<html lang="nl" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>'s Baby Shop - Collectie</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="bi bi-shop"></i> Tita's Baby Shop
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('basket') }}">
                            <i class="bi bi-basket"></i> Winkelmandje
                            <span class="badge bg-danger ms-1" id="basket-count" style="display: none;">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin') }}">
                            <i class="bi bi-shield-lock"></i> Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="bg-gradient py-5">
        <div class="container text-center">

            
            <!-- Search Bar -->
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <form method="GET" class="d-flex">
                        <input type="text" name="search" class="form-control me-2" 
                               placeholder="Zoeken naar kleding..." 
                               value="{{ filters.search }}">
                        <button type="submit" class="btn btn-light">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="container my-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-funnel"></i> Filters
                    <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearFilters()">
                        <i class="bi bi-x-circle"></i> Wissen
                    </button>
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" id="filterForm">
                    <div class="row g-3">
                        <!-- Gender Filter -->
                        <div class="col-md-2">
                            <label class="form-label">Geslacht</label>
                            <select name="geslacht" class="form-select" onchange="submitFilters()">
                                <option value="">Alle</option>
                                {% for option in config.geslacht %}
                                    <option value="{{ option }}" {% if filters.geslacht == option %}selected{% endif %}>
                                        {{ option }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Size Filter -->
                        <div class="col-md-2">
                            <label class="form-label">Maat</label>
                            <select name="maat" class="form-select" onchange="submitFilters()">
                                <option value="">Alle</option>
                                {% for option in config.maat %}
                                    <option value="{{ option }}" {% if filters.maat == option %}selected{% endif %}>
                                        {{ option }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Brand Filter -->
                        <div class="col-md-2">
                            <label class="form-label">Merk</label>
                            <select name="brand" class="form-select" onchange="submitFilters()">
                                <option value="">Alle</option>
                                {% for option in config.brand %}
                                    <option value="{{ option }}" {% if filters.brand == option %}selected{% endif %}>
                                        {{ option }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Category Filter -->
                        <div class="col-md-3">
                            <label class="form-label">Categorie</label>
                            <select name="category" class="form-select" onchange="submitFilters()">
                                <option value="">Alle</option>
                                {% for option in config.category %}
                                    <option value="{{ option }}" {% if filters.category == option %}selected{% endif %}>
                                        {{ option }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Sort Filter -->
                        <div class="col-md-3">
                            <label class="form-label">Sorteren</label>
                            <select name="sort" class="form-select" onchange="submitFilters()">
                                <option value="newest" {% if filters.sort == 'newest' %}selected{% endif %}>Nieuwste eerst</option>
                                <option value="oldest" {% if filters.sort == 'oldest' %}selected{% endif %}>Oudste eerst</option>
                                <option value="price_low" {% if filters.sort == 'price_low' %}selected{% endif %}>Prijs: laag naar hoog</option>
                                <option value="price_high" {% if filters.sort == 'price_high' %}selected{% endif %}>Prijs: hoog naar laag</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Hidden search field to preserve search when filtering -->
                    <input type="hidden" name="search" value="{{ filters.search }}">
                </form>
            </div>
        </div>
    </div>

    <!-- Results Info -->
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>
                <i class="bi bi-grid"></i> Collectie 
                <span class="badge bg-primary">{{ items|length }} item(s)</span>
            </h4>
            {% if filters.search or filters.geslacht or filters.maat or filters.brand or filters.category %}
                <small class="text-muted">
                    <i class="bi bi-filter"></i> Gefilterde resultaten
                </small>
            {% endif %}
        </div>
    </div>

    <!-- Items Gallery -->
    <div class="container mb-5">
        {% if items %}
            <div class="row g-4">
                {% for item in items %}
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card h-100 shadow-sm item-card">
                        <!-- Image -->
                        <div class="card-img-container">
                            {% if item.foto_filename %}
                                <img src="{{ url_for('static', filename='uploads/' + item.foto_filename) }}" 
                                     class="card-img-top" 
                                     style="height: 250px; object-fit: cover; cursor: pointer;"
                                     data-bs-toggle="modal" 
                                     data-bs-target="#itemModal{{ loop.index }}">
                            {% else %}
                                <div class="card-img-top d-flex align-items-center justify-content-center bg-secondary" 
                                     style="height: 250px;">
                                    <i class="bi bi-image display-4 text-muted"></i>
                                </div>
                            {% endif %}
                            
                            <!-- Price Badge -->
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-success fs-6">€{{ "%.2f"|format(item.prijs) }}</span>
                            </div>
                        </div>
                        
                        <!-- Card Body -->
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title fw-bold">{{ item.brand }}</h6>
                            <p class="card-text text-muted mb-2">{{ item.category }}</p>
                            
                            <!-- Item Details -->
                            <div class="mb-3">
                                <span class="badge bg-{{ 'primary' if item.geslacht == 'jongen' else 'danger' }} me-1">
                                    {{ item.geslacht }}
                                </span>
                                <span class="badge bg-secondary">Maat {{ item.maat }}</span>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="mt-auto">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <button class="btn btn-outline-primary w-100"
                                                data-bs-toggle="modal"
                                                data-bs-target="#itemModal{{ loop.index }}">
                                            <i class="bi bi-eye"></i> Details
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-success w-100"
                                                onclick="addToBasket('{{ item.id }}')"
                                                title="Voeg toe aan winkelmandje">
                                            <i class="bi bi-basket-plus"></i> Toevoegen
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Item Detail Modal -->
                <div class="modal fade" id="itemModal{{ loop.index }}" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">{{ item.brand }} - {{ item.category }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        {% if item.foto_filename %}
                                            <div class="position-relative">
                                                <img src="{{ url_for('static', filename='uploads/' + item.foto_filename) }}"
                                                     class="img-fluid rounded" id="modalImage{{ loop.index }}">

                                                <!-- Rotation Controls -->
                                                <div class="position-absolute top-0 end-0 m-2">
                                                    <div class="btn-group-vertical" role="group">
                                                        <button type="button" class="btn btn-sm btn-dark btn-outline-light"
                                                                onclick="rotateImage('{{ item.id }}', 90, {{ loop.index }})"
                                                                title="Draai 90° rechtsom">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-dark btn-outline-light"
                                                                onclick="rotateImage('{{ item.id }}', 270, {{ loop.index }})"
                                                                title="Draai 90° linksom">
                                                            <i class="bi bi-arrow-counterclockwise"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        {% else %}
                                            <div class="bg-secondary rounded d-flex align-items-center justify-content-center"
                                                 style="height: 300px;">
                                                <i class="bi bi-image display-1 text-muted"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <h4 class="text-success mb-3">€{{ "%.2f"|format(item.prijs) }}</h4>
                                        
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>Item ID:</strong></td>
                                                <td>
                                                    <code class="text-primary">{{ item.id }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1"
                                                            onclick="copyToClipboard('{{ item.id }}')"
                                                            title="Kopieer ID">
                                                        <i class="bi bi-clipboard"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Merk:</strong></td>
                                                <td>{{ item.brand }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Categorie:</strong></td>
                                                <td>{{ item.category }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Geslacht:</strong></td>
                                                <td>
                                                    <span class="badge bg-{{ 'primary' if item.geslacht == 'jongen' else 'danger' }}">
                                                        {{ item.geslacht }}
                                                    </span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Maat:</strong></td>
                                                <td><span class="badge bg-secondary">{{ item.maat }}</span></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Toegevoegd:</strong></td>
                                                <td>
                                                    <small class="text-muted">
                                                        {{ item.created_at.strftime('%d-%m-%Y') if item.created_at else 'Onbekend' }}
                                                    </small>
                                                </td>
                                            </tr>
                                        </table>
                                        
                                        <div class="mt-4">
                                            <button class="btn btn-success w-100 mb-3"
                                                    onclick="addToBasket('{{ item.id }}')"
                                                    data-bs-dismiss="modal">
                                                <i class="bi bi-basket-plus"></i> Voeg toe aan Winkelmandje
                                            </button>
                                            <p class="text-muted">
                                                <i class="bi bi-info-circle"></i>
                                                Voor meer informatie of om dit item te bestellen, neem contact met ons op.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- No Items Found -->
            <div class="text-center py-5">
                <i class="bi bi-search display-1 text-muted"></i>
                <h3 class="mt-3">Geen items gevonden</h3>
                <p class="text-muted">
                    {% if filters.search or filters.geslacht or filters.maat or filters.brand or filters.category %}
                        Probeer je filters aan te passen om meer resultaten te zien.
                    {% else %}
                        Er zijn momenteel geen items beschikbaar in onze collectie.
                    {% endif %}
                </p>
                <button class="btn btn-primary" onclick="clearFilters()">
                    <i class="bi bi-arrow-clockwise"></i> Alle Items Tonen
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="bi bi-shop"></i> Tita's Baby Shop</h5>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">&copy; 2025 Tita's Baby Shop</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Basket JS -->
    <script src="{{ url_for('static', filename='basket.js') }}"></script>
    
    <script>
        function submitFilters() {
            document.getElementById('filterForm').submit();
        }
        
        function clearFilters() {
            // Clear all form fields
            const form = document.getElementById('filterForm');
            const selects = form.querySelectorAll('select');
            selects.forEach(select => select.value = '');

            // Clear search parameter and submit
            const searchInput = form.querySelector('input[name="search"]');
            if (searchInput) searchInput.value = '';

            form.submit();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show success message using basket.js if available
                if (typeof basket !== 'undefined') {
                    basket.showMessage('ID gekopieerd naar klembord!', 'success');
                } else {
                    alert('ID gekopieerd naar klembord!');
                }
            }).catch(() => {
                alert('Kon ID niet kopiëren');
            });
        }

        function rotateImage(itemId, degrees, modalIndex) {
            // Show loading state
            const buttons = document.querySelectorAll(`#itemModal${modalIndex} .btn-group-vertical button`);
            buttons.forEach(btn => {
                btn.disabled = true;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
            });

            fetch(`/rotate_image_public/${itemId}/${degrees}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the image with cache busting
                    const img = document.getElementById(`modalImage${modalIndex}`);
                    const timestamp = new Date().getTime();
                    const currentSrc = img.src.split('?')[0]; // Remove existing query params
                    img.src = `${currentSrc}?t=${timestamp}`;

                    // Also update the card image
                    const cardImg = document.querySelector(`[data-bs-target="#itemModal${modalIndex}"]`);
                    if (cardImg && cardImg.tagName === 'IMG') {
                        cardImg.src = `${currentSrc}?t=${timestamp}`;
                    }

                    // Show success message
                    if (typeof basket !== 'undefined') {
                        basket.showMessage('Afbeelding gedraaid!', 'success');
                    }
                } else {
                    alert('Fout bij draaien van afbeelding: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Er is een fout opgetreden bij het draaien van de afbeelding.');
            })
            .finally(() => {
                // Restore buttons
                buttons[0].disabled = false;
                buttons[0].innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                buttons[1].disabled = false;
                buttons[1].innerHTML = '<i class="bi bi-arrow-counterclockwise"></i>';
            });
        }
        
        // Add hover effects to cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.item-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
    
    <style>
        .bg-gradient {
            background: linear-gradient(135deg, var(--bs-primary) 0%, #8b5cf6 100%);
        }
        
        .item-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .item-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.3) !important;
        }
        
        .card-img-container {
            position: relative;
            overflow: hidden;
        }
        
        .card-img-top {
            transition: transform 0.3s ease;
        }
        
        .card-img-container:hover .card-img-top {
            transform: scale(1.05);
        }
        
        .navbar-brand {
            font-size: 1.5rem;
        }
        
        .sticky-top {
            backdrop-filter: blur(10px);
        }

        .btn-group-vertical .btn {
            border-radius: 0.375rem !important;
            margin-bottom: 2px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .btn-group-vertical .btn:hover {
            opacity: 1;
        }

        .position-relative:hover .btn-group-vertical {
            opacity: 1;
        }

        .btn-group-vertical {
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }
    </style>
</body>
</html>
